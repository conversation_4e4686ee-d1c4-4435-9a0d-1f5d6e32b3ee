{"name": "1techacademy-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "if [ \"$VERCEL\" != \"1\" ]; then husky install; fi"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.7", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.2.2", "@radix-ui/react-collapsible": "^1.1.7", "@radix-ui/react-context-menu": "^2.2.11", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-hover-card": "^1.1.10", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.11", "@radix-ui/react-navigation-menu": "^1.2.9", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.3", "@radix-ui/react-scroll-area": "^1.2.5", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.8", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.3", "@radix-ui/react-visually-hidden": "^1.2.0", "@reduxjs/toolkit": "^2.7.0", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.2.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^2.30.0", "dom-to-image": "^2.6.0", "embla-carousel-react": "^8.6.0", "file-saver": "^2.0.5", "framer-motion": "^12.7.4", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "next": "15.3.1", "next-themes": "^0.4.6", "nookies": "^2.5.2", "papaparse": "^5.5.2", "phosphor-react": "^1.4.1", "react": "^18.2.0", "react-barcode": "^1.6.1", "react-day-picker": "8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.56.0", "react-hot-toast": "^2.5.2", "react-paystack": "^6.0.0", "react-qr-barcode-scanner": "^2.1.1", "react-redux": "^9.2.0", "react-resizable-panels": "^2.1.8", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "sonner": "^2.0.3", "stripe": "^18.0.0", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.8", "vaul": "^1.1.2", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/dom-to-image": "^2.6.7", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/papaparse": "^5.3.16", "@types/raf": "^3.4.3", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "cypress": "^14.3.1", "eslint": "^9", "eslint-config-next": "15.3.1", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.5.1", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}